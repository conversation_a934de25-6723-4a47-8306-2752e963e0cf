# Integration Guide: Assessment → Analysis → Archive

Panduan lengkap bagaimana Assessment Service, Analysis Worker, dan Archive Service bekerja bersama untuk memproses data assessment.

## 🔄 Alur <PERSON><PERSON><PERSON>

```
Client → API Gateway → Assessment Service → RabbitMQ → Analysis Worker → Archive Service
   ↓                                                                           ↓
   └─────────────── API Gateway ← Archive Service ←─────────────────────────────┘
                   (Mengambil hasil)
```

### 1. Client Mengirim Data Assessment

**Endpoint:** `POST /assessments/submit` (melalui API Gateway)

**Headers yang diperlukan:**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Format data yang harus dikirim:**
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  },
  "multipleIntelligences": {
    "linguistic": 85,
    "logicalMathematical": 90,
    "spatial": 75,
    "bodilyKinesthetic": 60,
    "musical": 55,
    "interpersonal": 70,
    "intrapersonal": 65,
    "naturalistic": 50
  },
  "cognitiveStyleIndex": {
    "analytic": 80,
    "intuitive": 60
  }
}
```

**Response dari Assessment Service:**
```json
{
  "success": true,
  "message": "Assessment submitted successfully for analysis",
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "remainingTokens": 4
  }
}
```

### 2. Assessment Service → RabbitMQ

Assessment Service akan:
1. Memvalidasi semua data assessment
2. Mengurangi token balance user
3. Membuat job ID unik
4. Mempublikasikan job ke RabbitMQ queue

**Message yang dikirim ke RabbitMQ:**
```json
{
  "jobId": "550e8400-e29b-41d4-a716-446655440000",
  "userId": "550e8400-e29b-41d4-a716-446655440001",
  "userEmail": "<EMAIL>",
  "assessmentData": {
    "riasec": { ... },
    "ocean": { ... },
    "viaIs": { ... },
    "multipleIntelligences": { ... },
    "cognitiveStyleIndex": { ... }
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "retryCount": 0
}
```

### 3. Analysis Worker → Archive Service

Analysis Worker akan:
1. Mengkonsumsi job dari RabbitMQ
2. Memproses data dengan Google Generative AI
3. Menyimpan hasil ke Archive Service

**Request ke Archive Service:**
```
POST /archive/results
Headers:
  X-Internal-Service: true
  X-Service-Key: internal_service_secret_key_change_in_production
  Content-Type: application/json
```

**Body request:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440001",
  "assessment_data": {
    "riasec": { ... },
    "ocean": { ... },
    "viaIs": { ... },
    "multipleIntelligences": { ... },
    "cognitiveStyleIndex": { ... }
  },
  "persona_profile": [
    {
      "archetype": "The Innovator",
      "shortSummary": "A creative problem-solver with strong analytical skills...",
      "strengths": ["Creative thinking", "Problem solving", "Analytical skills"],
      "weakness": ["Impatience", "Perfectionism", "Overthinking"],
      "careerRecommendation": [
        {
          "career": "Software Engineer",
          "reason": "Combines analytical thinking with creative problem-solving"
        }
      ],
      "insights": ["Focus on developing patience for long-term projects"],
      "workEnvironment": "Dynamic, collaborative environment with autonomy",
      "roleModel": ["Steve Jobs", "Elon Musk", "Marie Curie"]
    }
  ],
  "status": "completed"
}
```

**Response dari Archive Service:**
```json
{
  "success": true,
  "message": "Analysis result saved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "550e8400-e29b-41d4-a716-446655440001",
    "status": "completed",
    "created_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### 4. Client Mengambil Hasil

**Endpoint:** `GET /archive/results` (melalui API Gateway)

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "persona_profile": [ ... ],
        "status": "completed",
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

## 📋 Validasi Data

### Aturan Validasi Assessment Data:

1. **RIASEC (6 dimensi)**: realistic, investigative, artistic, social, enterprising, conventional
   - Semua field wajib ada
   - Nilai: integer 0-100

2. **OCEAN (5 dimensi)**: openness, conscientiousness, extraversion, agreeableness, neuroticism
   - Semua field wajib ada
   - Nilai: integer 0-100

3. **VIA-IS (24 karakter)**: creativity, curiosity, judgment, loveOfLearning, perspective, bravery, perseverance, honesty, zest, love, kindness, socialIntelligence, teamwork, fairness, leadership, forgiveness, humility, prudence, selfRegulation, appreciationOfBeauty, gratitude, hope, humor, spirituality
   - Semua 24 field wajib ada
   - Nilai: integer 0-100

4. **Multiple Intelligences (8 tipe)**: linguistic, logicalMathematical, spatial, bodilyKinesthetic, musical, interpersonal, intrapersonal, naturalistic
   - Semua field wajib ada
   - Nilai: integer 0-100

5. **Cognitive Style Index (2 dimensi)**: analytic, intuitive
   - Semua field wajib ada
   - Nilai: integer 0-100

## 🔐 Autentikasi

### User Authentication (Client → API Gateway):
- JWT Token dalam Authorization header
- Token balance minimal 1 untuk submit assessment

### Internal Service Authentication (Analysis Worker → Archive Service):
- Header `X-Internal-Service: true`
- Header `X-Service-Key: <INTERNAL_SERVICE_KEY>`

## ⚠️ Error Handling

### Kemungkinan Error dari Assessment Service:
- `VALIDATION_ERROR`: Data assessment tidak valid
- `INSUFFICIENT_TOKENS`: Token balance tidak cukup
- `UNAUTHORIZED`: JWT token tidak valid
- `QUEUE_ERROR`: Gagal mengirim ke RabbitMQ

### Kemungkinan Error dari Archive Service:
- `VALIDATION_ERROR`: Data persona profile tidak valid
- `INVALID_SERVICE_KEY`: Service key tidak valid
- `DATABASE_ERROR`: Gagal menyimpan ke database

## 🧪 Testing

Untuk menguji integrasi lengkap:

1. **Start semua service:**
   ```bash
   # Terminal 1: Assessment Service
   cd assessment-service && npm run dev
   
   # Terminal 2: Analysis Worker
   cd analysis-worker && npm run dev
   
   # Terminal 3: Archive Service
   cd archive-service && npm run dev
   
   # Terminal 4: API Gateway
   cd api-gateway && npm run dev
   ```

2. **Test dengan curl:**
   ```bash
   # Submit assessment
   curl -X POST http://localhost:3000/assessments/submit \
     -H "Authorization: Bearer <JWT_TOKEN>" \
     -H "Content-Type: application/json" \
     -d @test-assessment-data.json
   
   # Check results (tunggu 2-5 menit)
   curl -X GET http://localhost:3000/archive/results \
     -H "Authorization: Bearer <JWT_TOKEN>"
   ```

## 📊 Monitoring

Untuk memantau proses:
- **Assessment Service**: Log submission dan queue publishing
- **Analysis Worker**: Log job processing dan AI analysis
- **Archive Service**: Log result storage
- **RabbitMQ**: Monitor queue depth dan message processing
